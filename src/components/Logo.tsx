import React from 'react';
import { cn } from '@/lib/utils';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showTagline?: boolean;
  variant?: 'default' | 'white' | 'dark';
}

export const Logo: React.FC<LogoProps> = ({ 
  className, 
  size = 'md', 
  showTagline = true,
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: {
      container: 'text-lg sm:text-xl',
      cns: 'text-2xl sm:text-3xl',
      company: 'text-sm sm:text-base',
      tagline: 'text-xs sm:text-sm'
    },
    md: {
      container: 'text-xl sm:text-2xl',
      cns: 'text-3xl sm:text-4xl',
      company: 'text-base sm:text-lg',
      tagline: 'text-sm sm:text-base'
    },
    lg: {
      container: 'text-2xl sm:text-3xl',
      cns: 'text-4xl sm:text-5xl',
      company: 'text-lg sm:text-xl',
      tagline: 'text-base sm:text-lg'
    },
    xl: {
      container: 'text-3xl sm:text-4xl',
      cns: 'text-5xl sm:text-6xl',
      company: 'text-xl sm:text-2xl',
      tagline: 'text-lg sm:text-xl'
    }
  };

  const getTextColors = () => {
    switch (variant) {
      case 'white':
        return {
          c: 'text-red-500',
          n: 'text-white',
          s: 'text-blue-500',
          company: 'text-white',
          tagline: 'text-white/80'
        };
      case 'dark':
        return {
          c: 'text-red-500',
          n: 'text-gray-900',
          s: 'text-blue-600',
          company: 'text-gray-900',
          tagline: 'text-gray-700'
        };
      default:
        return {
          c: 'text-red-500',
          n: 'text-foreground',
          s: 'text-blue-600',
          company: 'text-foreground',
          tagline: 'text-muted-foreground'
        };
    }
  };

  const colors = getTextColors();
  const sizes = sizeClasses[size];

  return (
    <div className={cn('flex flex-col items-center space-y-1', className)}>
      {/* CNS Logo */}
      <div className={cn(
        'flex items-center font-bold tracking-tight transition-all duration-300',
        sizes.cns
      )}>
        <span 
          className={cn('transition-colors duration-300', colors.c)}
          style={{ fontFamily: 'Montserrat, sans-serif' }}
        >
          C
        </span>
        <span 
          className={cn('transition-colors duration-300', colors.n)}
          style={{ fontFamily: 'Montserrat, sans-serif' }}
        >
          N
        </span>
        <span 
          className={cn('transition-colors duration-300', colors.s)}
          style={{ fontFamily: 'Montserrat, sans-serif' }}
        >
          S
        </span>
      </div>

      {/* Company Name */}
      <div className={cn(
        'text-center font-medium tracking-wide transition-colors duration-300',
        sizes.company,
        colors.company
      )}>
        <span 
          style={{ fontFamily: 'Playfair Display, serif' }}
          className="italic"
        >
          Cable Network Solutions
        </span>
      </div>

      {/* Tagline */}
      {showTagline && (
        <div className={cn(
          'text-center font-normal tracking-wide transition-colors duration-300',
          sizes.tagline,
          colors.tagline
        )}>
          <span style={{ fontFamily: 'Montserrat, sans-serif' }}>
            A Telecommunications Company
          </span>
        </div>
      )}
    </div>
  );
};

// Compact version for navbar use
export const LogoCompact: React.FC<Omit<LogoProps, 'showTagline'>> = ({ 
  className, 
  size = 'sm',
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: 'text-xl sm:text-2xl',
    md: 'text-2xl sm:text-3xl',
    lg: 'text-3xl sm:text-4xl',
    xl: 'text-4xl sm:text-5xl'
  };

  const getTextColors = () => {
    switch (variant) {
      case 'white':
        return {
          c: 'text-red-500',
          n: 'text-white',
          s: 'text-blue-500'
        };
      case 'dark':
        return {
          c: 'text-red-500',
          n: 'text-gray-900',
          s: 'text-blue-600'
        };
      default:
        return {
          c: 'text-red-500',
          n: 'text-foreground',
          s: 'text-blue-600'
        };
    }
  };

  const colors = getTextColors();

  return (
    <div className={cn(
      'flex items-center font-bold tracking-tight transition-all duration-300',
      sizeClasses[size],
      className
    )}>
      <span 
        className={cn('transition-colors duration-300', colors.c)}
        style={{ fontFamily: 'Montserrat, sans-serif' }}
      >
        C
      </span>
      <span 
        className={cn('transition-colors duration-300', colors.n)}
        style={{ fontFamily: 'Montserrat, sans-serif' }}
      >
        N
      </span>
      <span 
        className={cn('transition-colors duration-300', colors.s)}
        style={{ fontFamily: 'Montserrat, sans-serif' }}
      >
        S
      </span>
    </div>
  );
};
