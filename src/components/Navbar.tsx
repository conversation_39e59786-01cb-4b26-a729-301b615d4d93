import { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ShoppingCart, Menu, X, ChevronDown, Cable, Network, HardDrive, Router, Radio } from 'lucide-react';
import { Button } from '@/components/ui/button';

import { Badge } from '@/components/ui/badge';
import { useCartStore } from '@/store/cartStore';
import { categories } from '@/lib/products';
import { CartSidebar } from './CartSidebar';
import { ThemeToggle } from './ThemeToggle';
import { LogoCompact } from './Logo';

export const Navbar = () => {
  const [isProductsOpen, setIsProductsOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const { getItemCount } = useCartStore();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);
  const closeTimerRef = useRef<number | null>(null);

  const cartItemCount = getItemCount();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsProductsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Prevent page scroll while interacting with the dropdown/popup (disconnect scroll)
  useEffect(() => {
    const el = popupRef.current;
    if (!el) return;

    const handleWheel = (e: WheelEvent) => {
      // stop propagation so the page doesn't scroll while the pointer is over the popup
      // if at top/bottom edge, prevent default so outer scroll won't kick in
      const delta = e.deltaY;
      const atTop = el.scrollTop === 0;
      const atBottom = Math.abs(el.scrollHeight - el.clientHeight - el.scrollTop) <= 1;

      if ((delta < 0 && atTop) || (delta > 0 && atBottom)) {
        e.preventDefault();
      }
      e.stopPropagation();
    };

    const handleTouchMove = (e: TouchEvent) => {
      e.stopPropagation();
    };

    if (isProductsOpen) {
      el.addEventListener('wheel', handleWheel as EventListener, { passive: false });
      el.addEventListener('touchmove', handleTouchMove as EventListener, { passive: false });
    }

    return () => {
      el.removeEventListener('wheel', handleWheel as EventListener);
      el.removeEventListener('touchmove', handleTouchMove as EventListener);
    };
  }, [isProductsOpen]);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const getIconComponent = (iconName: string) => {
    const iconMap: { [key: string]: any } = {
      'Cable': Cable,
      'Network': Network,
      'HardDrive': HardDrive,
      'Router': Router,
      'Radio': Radio,
    };
    return iconMap[iconName] || Network;
  };

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Media', href: '/news' },
    { name: 'Contact', href: '/contact' },
  ];

  const handleMouseEnter = () => {
    if (closeTimerRef.current) {
      clearTimeout(closeTimerRef.current);
      closeTimerRef.current = null;
    }
  };

  const handleMouseLeave = () => {
    // small delay so users can move between button and popup without it closing instantly
    closeTimerRef.current = window.setTimeout(() => {
      setIsProductsOpen(false);
      closeTimerRef.current = null;
    }, 180);
  };

  // cleanup on unmount
  useEffect(() => {
    return () => {
      if (closeTimerRef.current) {
        clearTimeout(closeTimerRef.current);
        closeTimerRef.current = null;
      }
    };
  }, []);

  return (
    <nav className="sticky top-0 z-50 w-full border-b card-gradient backdrop-blur-xl supports-[backdrop-filter]:bg-background/60">
      <div className="max-w-8xl mx-auto px-3 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 sm:h-18">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 flex-shrink-0">
            <div className="bg-white dark:bg-white rounded-lg px-2 py-1 border border-slate-200 dark:border-slate-700 shadow-sm transition-all duration-300 drop-shadow-[0_0_8px_rgba(255,255,255,0.4)]">
              <LogoCompact size="sm" variant="dark" />
            </div>
          </Link>



          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4 lg:space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`text-sm font-medium transition-all duration-200 hover:text-primary px-2 py-1 rounded-md hover:bg-accent/50 ${
                  isActive(item.href) ? 'text-primary bg-accent/30' : 'text-muted-foreground'
                }`}
              >
                {item.name}
              </Link>
            ))}
            
            {/* Products Dropdown */}
            <div className="relative" ref={dropdownRef} onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
              <button
                onClick={() => setIsProductsOpen(!isProductsOpen)}
                className="flex items-center text-sm font-medium text-muted-foreground hover:text-primary transition-all duration-200 px-2 py-1 rounded-md hover:bg-accent/50"
              >
                Products
                <ChevronDown className={`ml-1 h-4 w-4 transition-transform duration-200 ${isProductsOpen ? 'rotate-180' : ''}`} />
              </button>
              
              {isProductsOpen && (
                <div
                  ref={popupRef}
                  className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-[90vw] md:w-[72rem] lg:w-[80rem] max-w-[1200px] bg-white dark:bg-slate-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-slate-700 p-6 space-y-3 z-50 max-h-[calc(100vh-6rem)] overflow-y-auto transition-transform duration-200 ease-out"
                  role="menu"
                  aria-label="Product categories"
                >
                  <div className="text-xs font-semibold text-primary uppercase tracking-wider mb-4">
                    Our Product Categories
                  </div>
                  {categories.map((category) => (
                    <Link
                      key={category.id}
                      to={`/products/${category.slug}`}
                      className="group block p-4 rounded-lg hover:bg-gradient-to-r hover:from-primary/5 hover:to-primary/3 transition-all duration-200 border border-transparent hover:border-primary/10"
                      onClick={() => setIsProductsOpen(false)}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-white/10 dark:bg-white/5 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                          {(() => {
                            const IconComponent = getIconComponent(category.icon);
                            return <IconComponent className="h-5 w-5 text-primary-600 dark:text-primary-400 opacity-95" />;
                          })()}
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-foreground group-hover:text-primary transition-colors">
                            {category.name}
                          </div>
                          <div className="text-sm text-muted-foreground group-hover:text-foreground/70 transition-colors">
                            {category.description}
                          </div>
                        </div>
                        <ChevronDown className="h-4 w-4 text-muted-foreground group-hover:text-primary rotate-[-90deg] transition-all duration-200" />
                      </div>
                    </Link>
                  ))}
                  <div className="pt-3 mt-4 border-t border-border">
                    <Link
                      to="/products/all"
                      className="flex items-center justify-center space-x-2 text-sm text-primary hover:text-primary/80 transition-colors font-medium"
                      onClick={() => setIsProductsOpen(false)}
                    >
                      <span>View All Products</span>
                      <ChevronDown className="h-3 w-3 rotate-[-90deg]" />
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Cart, Theme Toggle and Mobile Menu */}
          <div className="flex items-center space-x-2">
            {/* Theme Toggle */}
            <ThemeToggle />

            <CartSidebar>
              <Button variant="ghost" size="icon" className="relative">
                <ShoppingCart className="h-5 w-5" />
                {cartItemCount > 0 && (
                  <Badge className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs">
                    {cartItemCount}
                  </Badge>
                )}
              </Button>
            </CartSidebar>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-border">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`block px-3 py-3 text-base font-medium transition-colors hover:text-primary rounded-lg hover:bg-accent/50 ${
                    isActive(item.href) ? 'text-primary bg-accent/30' : 'text-muted-foreground'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              <div className="pt-2">
                <div className="text-sm font-medium text-muted-foreground px-3 py-2">Products</div>
                {categories.map((category) => (
                  <Link
                    key={category.id}
                    to={`/products/${category.slug}`}
                    className="block px-6 py-3 text-sm text-muted-foreground hover:text-primary transition-colors rounded-lg hover:bg-accent/50"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {category.name}
                  </Link>
                ))}
                <Link
                  to="/products/all"
                  className="block px-6 py-3 text-sm font-medium text-primary hover:text-primary/80 transition-colors rounded-lg hover:bg-accent/50"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  View All Products
                </Link>
              </div>

              {/* Theme Toggle in Mobile Menu */}
              <div className="pt-4 border-t border-border mt-4">
                <div className="flex items-center justify-between px-3 py-2">
                  <span className="text-sm font-medium text-muted-foreground">Theme</span>
                  <ThemeToggle />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};