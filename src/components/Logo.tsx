import React from 'react';
import { cn } from '@/lib/utils';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showTagline?: boolean;
  variant?: 'default' | 'white' | 'dark';
}

export const Logo: React.FC<LogoProps> = ({ 
  className, 
  size = 'md', 
  showTagline = true,
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: {
      container: 'text-lg sm:text-xl',
      cns: 'text-2xl sm:text-3xl',
      company: 'text-sm sm:text-base',
      tagline: 'text-xs sm:text-sm'
    },
    md: {
      container: 'text-xl sm:text-2xl',
      cns: 'text-3xl sm:text-4xl',
      company: 'text-base sm:text-lg',
      tagline: 'text-sm sm:text-base'
    },
    lg: {
      container: 'text-2xl sm:text-3xl',
      cns: 'text-4xl sm:text-5xl',
      company: 'text-lg sm:text-xl',
      tagline: 'text-base sm:text-lg'
    },
    xl: {
      container: 'text-3xl sm:text-4xl',
      cns: 'text-5xl sm:text-6xl',
      company: 'text-xl sm:text-2xl',
      tagline: 'text-lg sm:text-xl'
    }
  };

  const getTextColors = () => {
    switch (variant) {
      case 'white':
        return {
          c: 'text-red-500',
          n: 'text-white',
          s: 'text-primary',
          company: 'text-white',
          tagline: 'text-white/80'
        };
      case 'dark':
        return {
          c: 'text-red-500',
          n: 'text-gray-900',
          s: 'text-primary',
          company: 'text-gray-900',
          tagline: 'text-gray-700'
        };
      default:
        return {
          c: 'text-red-500',
          n: 'text-foreground',
          s: 'text-primary',
          company: 'text-foreground',
          tagline: 'text-muted-foreground'
        };
    }
  };

  const colors = getTextColors();
  const sizes = sizeClasses[size];

  return (
    <div className={cn('flex flex-col items-center space-y-1', className)}>
      {/* CNS Logo */}
      <div className={cn(
        'flex items-center font-bold tracking-tight transition-all duration-300',
        sizes.cns
      )}>
        <span 
          className={cn('italic transition-colors duration-300', colors.c)}
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
        >
          C
        </span>
        <span 
          className={cn('italic transition-colors duration-300', colors.n)}
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
        >
          N
        </span>
        <span 
          className={cn('italic transition-colors duration-300', colors.s)}
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
        >
          S
        </span>
      </div>

      {/* Company Name */}
      <div className={cn(
        'text-center font-medium tracking-wide transition-colors duration-300',
        sizes.company,
        colors.company
      )}>
        <span 
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
          className="italic"
        >
          Cable Network Solutions
        </span>
      </div>

      {/* Tagline */}
      {showTagline && (
        <div className={cn(
          'text-center font-normal tracking-wide transition-colors duration-300',
          sizes.tagline,
          colors.tagline
        )}>
          <span 
            style={{ fontFamily: 'Times, "Times New Roman", serif' }}
            className="italic"
          >
            A Telecommunications Company
          </span>
        </div>
      )}
    </div>
  );
};

// Compact version for navbar use
export const LogoCompact: React.FC<Omit<LogoProps, 'showTagline'>> = ({ 
  className, 
  size = 'sm',
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: {
      cns: 'text-xl sm:text-2xl',
      company: 'text-xs sm:text-sm',
      tagline: 'text-[10px] sm:text-xs'
    },
    md: {
      cns: 'text-2xl sm:text-3xl',
      company: 'text-sm sm:text-base',
      tagline: 'text-xs sm:text-sm'
    },
    lg: {
      cns: 'text-3xl sm:text-4xl',
      company: 'text-base sm:text-lg',
      tagline: 'text-sm sm:text-base'
    },
    xl: {
      cns: 'text-4xl sm:text-5xl',
      company: 'text-lg sm:text-xl',
      tagline: 'text-base sm:text-lg'
    }
  };

  const getTextColors = () => {
    switch (variant) {
      case 'white':
        return {
          c: 'text-red-500',
          n: 'text-white',
          s: 'text-primary',
          company: 'text-white',
          tagline: 'text-white/80'
        };
      case 'dark':
        return {
          c: 'text-red-500',
          n: 'text-gray-900',
          s: 'text-primary',
          company: 'text-gray-900',
          tagline: 'text-gray-700'
        };
      default:
        return {
          c: 'text-red-500',
          n: 'text-foreground',
          s: 'text-primary',
          company: 'text-foreground',
          tagline: 'text-muted-foreground'
        };
    }
  };

  const colors = getTextColors();
  const sizes = sizeClasses[size];

  return (
    <div className={cn('flex flex-col items-center transition-all duration-300', className)} style={{ lineHeight: '0.7' }}>
      {/* CNS Logo */}
      <div className={cn(
        'flex items-center font-bold tracking-tight transition-all duration-300',
        sizes.cns
      )}>
        <span
          className={cn('italic transition-colors duration-300', colors.c)}
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
        >
          C
        </span>
        <span
          className={cn('italic transition-colors duration-300', colors.n)}
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
        >
          N
        </span>
        <span
          className={cn('italic transition-colors duration-300', colors.s)}
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
        >
          S
        </span>
      </div>

      {/* Company Name */}
      <div className={cn(
        'text-center font-medium tracking-wide transition-colors duration-300',
        sizes.company,
        colors.company
      )} style={{ lineHeight: '0.8', marginTop: '-2px' }}>
        <span
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
          className="italic"
        >
          Cable Network Solutions
        </span>
      </div>

      {/* Tagline */}
      <div className={cn(
        'text-center font-normal tracking-wide transition-colors duration-300',
        sizes.tagline,
        colors.tagline
      )} style={{ lineHeight: '0.8', marginTop: '-1px' }}>
        <span
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
          className="italic"
        >
          A Telecommunications Company
        </span>
      </div>
    </div>
  );
};

// Rounded/Circular version for navbar use
export const LogoRounded: React.FC<Omit<LogoProps, 'showTagline'>> = ({
  className,
  size = 'sm',
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: {
      cns: 'text-lg sm:text-xl',
      company: 'text-[7px] sm:text-[9px]',
      tagline: 'text-[6px] sm:text-[7px]'
    },
    md: {
      cns: 'text-xl sm:text-2xl',
      company: 'text-[10px] sm:text-xs',
      tagline: 'text-[8px] sm:text-[10px]'
    },
    lg: {
      cns: 'text-2xl sm:text-3xl',
      company: 'text-xs sm:text-sm',
      tagline: 'text-[10px] sm:text-xs'
    },
    xl: {
      cns: 'text-3xl sm:text-4xl',
      company: 'text-sm sm:text-base',
      tagline: 'text-xs sm:text-sm'
    }
  };

  const getTextColors = () => {
    switch (variant) {
      case 'white':
        return {
          c: 'text-red-500',
          n: 'text-white',
          s: 'text-primary',
          company: 'text-white',
          tagline: 'text-white/80'
        };
      case 'dark':
        return {
          c: 'text-red-500',
          n: 'text-gray-900',
          s: 'text-primary',
          company: 'text-gray-900',
          tagline: 'text-gray-700'
        };
      default:
        return {
          c: 'text-red-500',
          n: 'text-foreground',
          s: 'text-primary',
          company: 'text-foreground',
          tagline: 'text-muted-foreground'
        };
    }
  };

  const colors = getTextColors();
  const sizes = sizeClasses[size];

  return (
    <div className={cn('flex flex-col items-center justify-center transition-all duration-300', className)}
         style={{ lineHeight: '0.6' }}>
      {/* CNS Logo */}
      <div className={cn(
        'flex items-center font-bold tracking-tighter transition-all duration-300',
        sizes.cns
      )}>
        <span
          className={cn('italic transition-colors duration-300', colors.c)}
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
        >
          C
        </span>
        <span
          className={cn('italic transition-colors duration-300', colors.n)}
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
        >
          N
        </span>
        <span
          className={cn('italic transition-colors duration-300', colors.s)}
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
        >
          S
        </span>
      </div>

      {/* Company Name - Single Line */}
      <div className={cn(
        'text-center font-medium tracking-tighter transition-colors duration-300 whitespace-nowrap',
        sizes.company,
        colors.company
      )} style={{ lineHeight: '0.8', marginTop: '-1px' }}>
        <span
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
          className="italic"
        >
          Cable Network Solutions
        </span>
      </div>

      {/* Tagline - Shortened */}
      <div className={cn(
        'text-center font-light tracking-wider transition-colors duration-300',
        sizes.tagline,
        colors.tagline
      )} style={{ lineHeight: '0.8', marginTop: '0px' }}>
        <span
          style={{ fontFamily: 'Times, "Times New Roman", serif' }}
          className="italic"
        >
          Telecommunications
        </span>
      </div>
    </div>
  );
};
